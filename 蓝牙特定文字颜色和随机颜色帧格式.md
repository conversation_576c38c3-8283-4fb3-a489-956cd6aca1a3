# 蓝牙特定文字颜色和随机颜色帧格式文档

## 📋 概述

本文档描述两种高级颜色功能的蓝牙帧格式：

1. **特定文字颜色功能**：为文本中的每个字符单独设置不同的颜色，实现丰富的文字显示效果。例如在"这部手机售价 100 元"中，可以单独将"100"设置为红色，其他字符保持默认颜色。

2. **随机颜色功能**：为文本设置各种随机颜色模式，包括每字符不同随机色、彩虹色序列、暖色系/冷色系随机等，并支持定时变化效果。

## 🔧 命令定义

### 特定字符颜色命令 (0x0A)

- **命令代码**: `0x0A` (`BT_CMD_SET_SPECIFIC_COLOR`)
- **数据长度**: `5字节` (`BT_SPECIFIC_COLOR_DATA_LEN`)
- **功能**: 设置指定字符的特定颜色

### 随机颜色命令 (0x0B)

- **命令代码**: `0x0B` (`BT_CMD_SET_RANDOM_COLOR`)
- **数据长度**: `4字节` (`BT_RANDOM_COLOR_DATA_LEN`)
- **功能**: 设置文本随机颜色模式

## 📦 帧格式

### 特定字符颜色帧结构 (0x0A)

```
| 帧头1 | 帧头2 | 命令 | 数据长度 | 数据区域 | 帧尾1 | 帧尾2 |
|-------|-------|------|----------|----------|-------|-------|
| 0xAA  | 0x55  | 0x0A |   0x05   | 5字节数据 | 0x0D  | 0x0A  |
```

#### 特定字符颜色数据区域 (5 字节)

```
| 字节位置 | 字段名称 | 数据类型 | 取值范围 | 说明 |
|----------|----------|----------|----------|------|
| 0        | 屏幕区域 | uint8_t  | 0x01-0x02 | 目标屏幕区域 |
| 1        | 字符索引 | uint8_t  | 0-255    | 字符在屏幕中的索引位置 |
| 2        | 红色分量 | uint8_t  | 0-255    | RGB红色分量 |
| 3        | 绿色分量 | uint8_t  | 0-255    | RGB绿色分量 |
| 4        | 蓝色分量 | uint8_t  | 0-255    | RGB蓝色分量 |
```

### 随机颜色帧结构 (0x0B)

```
| 帧头1 | 帧头2 | 命令 | 数据长度 | 数据区域 | 帧尾1 | 帧尾2 |
|-------|-------|------|----------|----------|-------|-------|
| 0xAA  | 0x55  | 0x0B |   0x04   | 4字节数据 | 0x0D  | 0x0A  |
```

#### 随机颜色数据区域 (4 字节)

```
| 字节位置 | 字段名称 | 数据类型 | 取值范围 | 说明 |
|----------|----------|----------|----------|------|
| 0        | 屏幕区域 | uint8_t  | 0x01-0x03 | 目标屏幕区域 |
| 1        | 随机模式 | uint8_t  | 0x00-0x06 | 随机颜色模式 |
| 2        | 更新间隔 | uint8_t  | 1-10     | 颜色更新间隔(秒) |
| 3        | 随机种子 | uint8_t  | 0-255    | 随机数生成种子 |
```

## 📊 参数说明

### 屏幕区域 (第 0 字节)

#### 特定字符颜色命令 (0x0A)

| 数值 | 宏定义            | 说明   | 字符索引范围         |
| ---- | ----------------- | ------ | -------------------- |
| 0x01 | `BT_SCREEN_UPPER` | 上半屏 | 0 ~ (上半屏字符数-1) |
| 0x02 | `BT_SCREEN_LOWER` | 下半屏 | 0 ~ (下半屏字符数-1) |

> **注意**: 特定字符颜色不支持 `BT_SCREEN_BOTH` (0x03)，上下半屏需分别设置。

#### 随机颜色命令 (0x0B)

| 数值 | 宏定义           | 说明 | 应用范围           |
| ---- | ---------------- | ---- | ------------------ |
| 0x03 | `BT_SCREEN_BOTH` | 全屏 | 上下半屏统一随机色 |

> **重要限制**: 随机颜色仅支持全屏设置 (0x03)，不支持单独设置上半屏或下半屏。

> **注意**: 上下半屏的字符索引是独立的，都从 0 开始计算。

### 字符索引 (第 1 字节)

- **上半屏**: 如果有 6 个字符，索引范围为 0-5
- **下半屏**: 如果有 5 个字符，索引范围为 0-4
- **索引计算**: 从左到右（或从上到下，取决于显示方向）依次编号
- **越界处理**: 超出范围的索引会被忽略并输出错误信息

### RGB 颜色分量 (特定字符颜色第 2-4 字节)

- **取值范围**: 每个分量 0-255
- **颜色格式**: 标准 RGB888 格式
- **转换**: 系统内部会自动转换为 RGB565 格式用于显示

### 随机颜色模式 (随机颜色第 1 字节)

| 数值 | 宏定义                   | 说明               | 效果描述                     |
| ---- | ------------------------ | ------------------ | ---------------------------- |
| 0x00 | `RANDOM_COLOR_OFF`       | 关闭随机色         | 清除随机颜色设置             |
| 0x01 | `RANDOM_COLOR_EACH_CHAR` | 每个字符不同随机色 | 每个字符独立的随机颜色       |
| 0x02 | `RANDOM_COLOR_ALL_SAME`  | 所有字符相同随机色 | 全体字符同一随机色，定时变化 |
| 0x03 | `RANDOM_COLOR_RAINBOW`   | 彩虹色随机序列     | 彩虹色谱中的随机颜色         |
| 0x04 | `RANDOM_COLOR_WARM`      | 暖色系随机         | 红、橙、黄色系的随机颜色     |
| 0x05 | `RANDOM_COLOR_COOL`      | 冷色系随机         | 蓝、青、紫色系的随机颜色     |
| 0x06 | `RANDOM_COLOR_BRIGHT`    | 高亮度随机色       | 高饱和度、高亮度的随机颜色   |

### 更新间隔 (随机颜色第 2 字节)

- **取值范围**: 1-10 秒
- **功能**: 控制随机颜色的变化频率
- **适用模式**: 主要对 `RANDOM_COLOR_ALL_SAME` 模式有效，其他模式颜色相对稳定

### 随机种子 (随机颜色第 3 字节)

- **取值范围**: 0-255
- **功能**: 控制随机数生成的起始值
- **特性**: 相同种子产生相同的随机序列，确保颜色的一致性和可重现性

## 🎨 预定义颜色参考

| 颜色名称 | RGB 值        | 十六进制 | 用途示例       |
| -------- | ------------- | -------- | -------------- |
| 红色     | (255,0,0)     | 0xFF0000 | 突出显示、警告 |
| 绿色     | (0,255,0)     | 0x00FF00 | 成功状态、确认 |
| 蓝色     | (0,0,255)     | 0x0000FF | 链接、信息     |
| 黄色     | (255,255,0)   | 0xFFFF00 | 注意、高亮     |
| 紫色     | (255,0,255)   | 0xFF00FF | 特殊标记       |
| 青色     | (0,255,255)   | 0x00FFFF | 辅助信息       |
| 白色     | (255,255,255) | 0xFFFFFF | 默认颜色       |

## 📝 使用示例

### 示例 1: 设置上半屏第 2 个字符为红色

```
帧数据: AA 55 0A 05 01 02 FF 00 00 0D 0A
解析:
- 帧头: AA 55
- 命令: 0A (设置特定字符颜色)
- 数据长度: 05
- 屏幕区域: 01 (上半屏)
- 字符索引: 02 (第3个字符，从0开始)
- RGB颜色: FF 00 00 (红色)
- 帧尾: 0D 0A
```

### 示例 2: 设置下半屏第 0 个字符为绿色

```
帧数据: AA 55 0A 05 02 00 00 FF 00 0D 0A
解析:
- 屏幕区域: 02 (下半屏)
- 字符索引: 00 (第1个字符)
- RGB颜色: 00 FF 00 (绿色)
```

### 示例 3: 设置上半屏第 5 个字符为黄色

```
帧数据: AA 55 0A 05 01 05 FF FF 00 0D 0A
解析:
- 屏幕区域: 01 (上半屏)
- 字符索引: 05 (第6个字符)
- RGB颜色: FF FF 00 (黄色)
```

### 示例 4: 设置全屏彩虹色随机模式

```
帧数据: AA 55 0B 04 03 03 02 7B 0D 0A
解析:
- 帧头: AA 55
- 命令: 0B (设置随机颜色)
- 数据长度: 04
- 屏幕区域: 03 (全屏)
- 随机模式: 03 (彩虹色随机序列)
- 更新间隔: 02 (2秒)
- 随机种子: 7B (123)
- 帧尾: 0D 0A
```

### 示例 5: 设置上半屏每字符不同随机色

```
帧数据: AA 55 0B 04 01 01 01 FF 0D 0A
解析:
- 屏幕区域: 01 (上半屏)
- 随机模式: 01 (每个字符不同随机色)
- 更新间隔: 01 (1秒)
- 随机种子: FF (255)
```

### 示例 6: 关闭随机颜色

```
帧数据: AA 55 0B 04 03 00 01 00 0D 0A
解析:
- 屏幕区域: 03 (全屏)
- 随机模式: 00 (关闭随机色)
- 更新间隔: 01 (无效参数)
- 随机种子: 00 (无效参数)
```

## ⚠️ 重要限制和注意事项

### 颜色模式互斥性

#### 特定字符颜色 (0x0A)

- ✅ **支持组合**: 特定字符颜色 + 呼吸特效
- ✅ **支持组合**: 特定字符颜色 + 滚动特效
- ✅ **支持组合**: 特定字符颜色 + 闪烁特效
- ❌ **互斥限制**: 不能与渐变色模式同时使用
- ❌ **互斥限制**: 不能与随机颜色模式同时使用

#### 随机颜色 (0x0B)

- ✅ **支持组合**: 随机颜色 + 呼吸特效
- ✅ **支持组合**: 随机颜色 + 滚动特效
- ✅ **支持组合**: 随机颜色 + 闪烁特效
- ❌ **互斥限制**: 不能与渐变色模式同时使用
- ❌ **互斥限制**: 不能与特定字符颜色模式同时使用

> **优先级规则**: 后设置的颜色模式会覆盖先设置的模式

### 内存管理

- 系统会动态分配内存存储每个字符的颜色信息
- 上下半屏的颜色数据独立存储和管理
- 切换到其他颜色模式时会自动释放内存

### 索引范围验证

- 系统会验证字符索引是否在有效范围内
- 超出范围的索引会被忽略，不会导致系统崩溃
- 建议发送命令前确认当前文本的字符数量

### 显示优先级

- 特定字符颜色的优先级高于默认固定颜色
- 没有设置特定颜色的字符会使用默认的上/下半屏文字颜色
- 支持与呼吸特效叠加，呼吸效果会应用到特定颜色上

## 🔄 清除特定颜色

要清除所有特定字符颜色设置，需要切换到其他颜色模式：

### 方法 1: 切换到固定色模式

发送固定色命令 (`0x06`) 会自动清除特定字符颜色设置

### 方法 2: 切换到随机色模式

发送随机色命令 (`0x0B`) 会自动清除特定字符颜色设置

### 方法 3: 切换到渐变色模式

发送渐变色命令 (`0x06`) 会自动清除特定字符颜色设置

## 🛠️ 错误处理

### 特定字符颜色错误处理

| 错误类型     | 错误信息                                   | 原因                              | 解决方案                   |
| ------------ | ------------------------------------------ | --------------------------------- | -------------------------- |
| 数据长度错误 | "错误: 特定字符颜色数据长度不足"           | 数据区域不足 5 字节               | 确保发送完整的 5 字节数据  |
| 屏幕区域无效 | "错误: 不支持的屏幕区域"                   | 屏幕区域参数不在 0x01-0x02 范围内 | 使用有效的屏幕区域值       |
| 索引超出范围 | "错误: 字符索引超出范围"                   | 字符索引超出当前文本长度          | 确认文本长度后设置正确索引 |
| 模式冲突     | "错误: 渐变色模式下不允许设置特定字符颜色" | 当前处于互斥的颜色模式            | 先切换到兼容的颜色模式     |

### 随机颜色错误处理

| 错误类型     | 错误信息                       | 原因                              | 解决方案                   |
| ------------ | ------------------------------ | --------------------------------- | -------------------------- |
| 数据长度错误 | "错误: 随机颜色数据长度不足"   | 数据区域不足 4 字节               | 确保发送完整的 4 字节数据  |
| 屏幕区域无效 | "错误: 随机颜色仅支持全屏设置" | 屏幕区域参数不是 0x03             | 使用 0x03 (全屏) 设置      |
| 模式无效     | "错误: 无效的随机颜色模式"     | 随机模式参数不在 0x00-0x06 范围内 | 使用有效的随机颜色模式     |
| 间隔无效     | "错误: 无效的更新间隔"         | 更新间隔不在 1-10 范围内          | 设置 1-10 秒的有效间隔     |
| 文本为空     | "错误: 没有文本可设置随机颜色" | 当前没有显示文本                  | 先发送文本命令设置显示内容 |

## 📈 性能考虑

### 内存使用

#### 特定字符颜色

- 每个字符需要额外的 3 字节存储(颜色值+标志位)
- 上下半屏分别管理，最大支持约 100 个字符的特定颜色设置
- 系统会根据实际文本长度动态分配内存

#### 随机颜色

- 每个字符需要 2 字节存储随机颜色值
- 内存使用量取决于当前显示的文本长度
- 支持上下半屏统一管理或独立管理

### 刷新性能

#### 特定字符颜色

- 不会显著影响显示性能
- 与固定色模式性能基本相同
- 支持与所有兼容的特效组合使用

#### 随机颜色

- 定时更新可能会有轻微的性能影响
- `RANDOM_COLOR_EACH_CHAR` 模式性能最佳（颜色相对稳定）
- `RANDOM_COLOR_ALL_SAME` 模式会定期重新生成颜色

### 建议使用场景

#### 特定字符颜色

- 突出显示关键信息（如价格、数字、重要词汇）
- 创建多彩的标题效果
- 实现简单的语法高亮

#### 随机颜色

- 创建动态的彩色效果
- 节日庆典或活动展示
- 吸引注意力的广告显示
- 儿童或娱乐相关的应用场景

## 🔄 颜色模式优先级和切换行为

### 模式切换规则

系统采用 **"后设置覆盖前设置"** 的优先级机制：

1. **随机颜色 → 固定颜色**：

   - 发送固定颜色命令会自动清除随机颜色设置
   - 系统切换到固定色模式
   - 示例：设置全屏随机色后，单独设置上半屏青色，结果是**整个屏幕都变成青色**

2. **随机颜色 → 特定字符颜色**：

   - 发送特定字符颜色命令会自动清除随机颜色设置
   - 系统切换到特定字符颜色模式
   - 未设置特定颜色的字符使用默认的上/下半屏固定色

3. **随机颜色 → 渐变颜色**：
   - 发送渐变色命令会自动清除随机颜色设置
   - 系统切换到渐变色模式

### 实际应用示例

**场景**: 先设置全屏彩虹随机色，再设置上半屏青色

```
步骤1: AA 55 0B 04 03 03 02 7B 0D 0A  (全屏彩虹随机色)
结果: 上下半屏都显示彩虹随机色

步骤2: AA 55 06 07 01 01 00 00 FF FF 00 0D 0A  (上半屏青色)
结果: 整个屏幕都变成青色（随机色被完全覆盖）
```

> **重要**: 由于颜色模式是全局的，设置任何固定颜色都会影响整个系统的颜色模式，而不仅仅是指定的屏幕区域。

### 文本变更时的自动清理

为了防止新文本意外继承旧文本的特定字符颜色，系统在设置新文本时会自动执行以下清理操作：

1. **全屏文本更新** (`BT_SCREEN_BOTH`)：

   - 自动清除所有特定字符颜色设置
   - 系统切换到固定色模式

2. **上半屏文本更新** (`BT_SCREEN_UPPER`)：

   - 仅清除上半屏的特定字符颜色设置
   - 保留下半屏的特定字符颜色设置
   - 如果下半屏也没有特定颜色，则切换到固定色模式

3. **下半屏文本更新** (`BT_SCREEN_LOWER`)：
   - 仅清除下半屏的特定字符颜色设置
   - 保留上半屏的特定字符颜色设置
   - 如果上半屏也没有特定颜色，则切换到固定色模式

**实际应用示例**：

```
步骤1: 设置文本 "Hello World" + 特定字符颜色
步骤2: 设置新文本 "New Text"
结果: 新文本使用默认颜色，不会继承 "Hello World" 的特定字符颜色
```

---

_文档版本: v1.1_  
_最后更新: 2024 年_  
_适用固件版本: v2.0+_
